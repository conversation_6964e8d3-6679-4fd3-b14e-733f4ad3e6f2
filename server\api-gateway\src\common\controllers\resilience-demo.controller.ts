/**
 * 弹性示例控制器
 * 用于演示熔断器和限流器的使用
 */
import { Controller, Get, Post, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UseCircuitBreaker } from '@shared/circuit-breaker';
import { UseRateLimiter, RateLimiterType } from '@shared/rate-limiter';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom, timeout } from 'rxjs';

@ApiTags('弹性示例')
@Controller('resilience-demo')
export class ResilienceDemoController {
  private readonly logger = new Logger(ResilienceDemoController.name);

  constructor(
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  /**
   * 测试熔断器
   * 使用熔断器保护对用户服务的调用
   */
  @Get('circuit-breaker-test')
  @UseCircuitBreaker('user-service', {
    failureThreshold: 3,
    successThreshold: 2,
    timeout: 5000,
    resetTimeout: 10000,
    enableFallback: true,
    fallback: () => ({ message: '用户服务不可用，请稍后再试' }),
  })
  @ApiOperation({ summary: '测试熔断器' })
  @ApiResponse({ status: 200, description: '成功' })
  async testCircuitBreaker() {
    try {
      // 调用用户服务，可能会超时或失败
      const result = await firstValueFrom(
        this.userService.send({ cmd: 'ping' }, {}).pipe(timeout(3000))
      );
      return { message: '调用成功', result };
    } catch (error) {
      this.logger.error(`调用用户服务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 测试限流器
   * 使用令牌桶限流器限制API调用频率
   */
  @Get('rate-limiter-test')
  @UseRateLimiter('api-rate-limiter', {
    type: RateLimiterType.TOKEN_BUCKET,
    windowMs: 60000, // 1分钟
    maxRequests: 5, // 每分钟最多5个请求
  })
  @ApiOperation({ summary: '测试限流器' })
  @ApiResponse({ status: 200, description: '成功' })
  @ApiResponse({ status: 429, description: '请求过于频繁' })
  async testRateLimiter() {
    return { message: '请求成功', timestamp: new Date() };
  }

  /**
   * 测试滑动窗口限流器
   * 使用滑动窗口限流器限制API调用频率
   */
  @Get('sliding-window-test')
  @UseRateLimiter('sliding-window-limiter', {
    type: RateLimiterType.SLIDING_WINDOW,
    windowMs: 60000, // 1分钟
    maxRequests: 5, // 每分钟最多5个请求
  })
  @ApiOperation({ summary: '测试滑动窗口限流器' })
  @ApiResponse({ status: 200, description: '成功' })
  @ApiResponse({ status: 429, description: '请求过于频繁' })
  async testSlidingWindow() {
    return { message: '请求成功', timestamp: new Date() };
  }

  /**
   * 测试服务调用
   * 使用熔断器和限流器保护对项目服务的调用
   */
  @Post('service-call/:serviceId')
  @UseCircuitBreaker('project-service')
  @UseRateLimiter('service-call-limiter', {
    type: RateLimiterType.TOKEN_BUCKET,
    windowMs: 60000, // 1分钟
    maxRequests: 10, // 每分钟最多10个请求
  })
  @ApiOperation({ summary: '测试服务调用' })
  @ApiResponse({ status: 200, description: '成功' })
  async testServiceCall(
    @Param('serviceId') serviceId: string,
    @Body() data: any,
  ) {
    try {
      // 调用项目服务
      const result = await firstValueFrom(
        this.projectService.send({ cmd: 'processData', serviceId }, data).pipe(timeout(5000))
      );
      return { message: '调用成功', result };
    } catch (error) {
      this.logger.error(`调用项目服务失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
